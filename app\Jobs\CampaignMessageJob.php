<?php

namespace App\Jobs;

use Log;
use Exception;
use Throwable;
use App\Helpers\Func;
use App\Models\Dialog;
use App\Models\HubList;
use App\Models\Message;
use App\Hubspot\Hubspot;
use App\Models\Campaign;
use App\Helpers\HelperTrait;
use Illuminate\Bus\Queueable;
use App\Services\Whatsapp\SendService;
use Illuminate\Queue\InteractsWithQueue;
use App\Services\Whatsapp\TemplateService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\DB;
use App\Jobs\CampaignBatchJob;
use App\Jobs\MessageBatchJob;

class CampaignMessageJob implements ShouldQueue
{
    use Dispatchable;
    use HelperTrait;
    use InteractsWithQueue;
    use Queueable;

    protected $contact;

    protected $template;

    protected $accountId;

    protected $requestId;

    protected $hubspotApp;

    protected $properties;

    protected $campaignId;

    public function __construct($data, $requestId)
    {
        $this->requestId = $requestId;
        $this->campaignId = $data->campaignId;
        $this->contact = $data->contact;
        $this->template = $data->template;
        $this->accountId = $data->accountId;
        $this->properties = $data->properties;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Start overall execution timer
        $startOverall = microtime(true);
        Log::info("[CampaignMessageJob:handle] Running for $this->requestId, AccountId: $this->accountId");

        // Initialize timeline data
        $timelineData = null;

        try {
            // Start initialization timer
            $startInit = microtime(true);
            $waba = new SendService($this->accountId, $this->requestId);

            $options = [];
            $portal = $waba->account;
            $hsApp = new Hubspot($portal->portal_id, $this->requestId);
            $phone = $this->contact->properties->phone ?? $this->contact->properties->mobilephone ?? '';

            $email = $this->contact->properties->email ?? '';
            $firstname = $this->contact->properties->firstname ?? '';
            $lastname = $this->contact->properties->lastname ?? '';
            $name = trim("$firstname $lastname");

            // build campaign data
            $campaign = [
                'campaign_id' => $this->campaignId,
                'account_id' => $this->accountId,
                'portal_id' => $portal->portal_id,
                'object_id' => $this->contact->id,
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'status' => 'sent',
            ];
            $initDuration = round((microtime(true) - $startInit) * 1000, 2);
            Log::info("[CampaignMessageJob:handle] $this->requestId - Initialization completed in {$initDuration}ms");

            // Start validation timer
            $startValidation = microtime(true);
            if (! $phone) {
                throw new Exception("contact doesn't have phone number", 1); // NOSONAR
            }

            $phone = $options['phone'] = Func::makeChatId($phone);
            $validationDuration = round((microtime(true) - $startValidation) * 1000, 2);
            Log::info("[CampaignMessageJob:handle] $this->requestId - Validation completed in {$validationDuration}ms");

            // Start template fetching timer
            $startTemplateFetch = microtime(true);
            if (config('services.whatsapp.use_mock_api')) {
                $response = Http::get(config('services.whatsapp.mock_api_url').'templates/123456789');
                $template = json_decode($response->body())->template;
                Log::info('[CampaignMessageJob:handle] Mock Template'.json_encode($template));
                Log::info('[CampaignMessageJob:handle] Selected Template'.json_encode($this->template));
            } else {
                $template = $waba->fetchTemplateById($this->template->id);
                if (! $template) {
                    throw new Exception('template not found', 1); // NOSONAR
                }

                $templateService = new TemplateService($template);
                $template = $templateService->analyzeTemplate();
            }
            $templateFetchDuration = round((microtime(true) - $startTemplateFetch) * 1000, 2);
            Log::info("[CampaignMessageJob:handle] $this->requestId - Template fetch and analysis completed in {$templateFetchDuration}ms");

            // Start parameter preparation timer
            $startParamPrep = microtime(true);
            $options['params'] = [];
            $options['templateData'] = [];
            $options['template'] = $template;
            Log::info('[CampaignMessageJob:handle] options'.json_encode($options));
            // build params
            $fields = (array) $this->template->fields ?? [];

            $namedParams = $template->named ?? [];
            $paramCount = $template->params;
            for ($i = 0; $i < $paramCount; $i++) {
                $key = $i + 1;
                $param = [
                    'type' => 'text',
                    'text' => $fields['placeholder_'.$key] ?? 'Default Name',
                ];
                if (isset($namedParams[$i])) {
                    $param['parameter_name'] = $namedParams[$i];
                }
                $options['params'][] = $param;
                $options['templateData'][] = $fields['placeholder_'.$key] ?? 'Default Name';
            }

            $options = array_merge($options, (array) $fields);
            Log::info('[CampaignMessageJob:handle] options'.json_encode($options));
            $paramPrepDuration = round((microtime(true) - $startParamPrep) * 1000, 2);
            Log::info("[CampaignMessageJob:handle] $this->requestId - Parameter preparation completed in {$paramPrepDuration}ms");

            // Start API call timer
            $startApiCall = microtime(true);
            if (config('services.whatsapp.use_mock_api')) {
                $randomPhone = mt_rand(1000000000, 9999999999); // Already 10 digits
                $phone = $options['phone'] = Func::makeChatId('+91'.$randomPhone);
                $sendTemplate = Http::post(config('services.whatsapp.mock_api_url').'whatsapp/send-template');
                $res = json_decode($sendTemplate->body());
                $res->customData = $waba->makeCustomData($res);

            } else {
                if ($template->type && $template->type != 'text') {
                    $res = $waba->sendMediaTemplate($options);
                } else {
                    $res = $waba->sendTemplate($options);
                }
            }

            $campaign['request_data'] = $waba->request;
            $campaign['response_data'] = $waba->response;
            $apiCallDuration = round((microtime(true) - $startApiCall) * 1000, 2);
            Log::info("[CampaignMessageJob:handle] $this->requestId - WhatsApp API call completed in {$apiCallDuration}ms");

            // Start response validation timer
            $startRespValidation = microtime(true);
            if ($res === false || ! isset($res->customData['messageId'])) {
                throw new Exception('unable to send text message', 1); // NOSONAR
            }
            $campaign['message_id'] = $res->customData['messageId'];

            $messageBody = $template->body;
            for ($i = 0; $i < count($options['templateData']); $i++) {
                $key = $i + 1;
                $messageBody = str_replace('{{'.$key.'}}', $options['templateData'][$i], $messageBody);
            }
            $respValidationDuration = round((microtime(true) - $startRespValidation) * 1000, 2);
            Log::info("[CampaignMessageJob:handle] $this->requestId - Response validation completed in {$respValidationDuration}ms");

            // Start dialog save timer
            $startDialogSave = microtime(true);
            // $name = $this->getNameFromProps($properties);
            Dialog::updateOrCreate(['chatId' => $options['phone'], 'account_id' => $this->accountId], [
                'object_id' => $this->contact->id,
                'phone' => $phone,
                'name' => $name,
                'time' => time(),
            ]);
            $dialogSaveDuration = round((microtime(true) - $startDialogSave) * 1000, 2);
            Log::info("[CampaignMessageJob:handle] $this->requestId - Dialog save completed in {$dialogSaveDuration}ms");

            // Start message preparation and save timer
            $startMsgSave = microtime(true);
            $type = $template->type ?? 'text';
            if (! in_array($type, ['image', 'audio', 'video', 'document'])) {
                $type = 'text';
            }
            $message = [
                'account_id' => $this->accountId,
                'id' => $res->customData['messageId'],
                'chatId' => $options['phone'],
                'type' => strtolower($type),
                'body' => $messageBody,
                'from' => $res->customData['from'],
                'to' => $phone,
                'fromMe' => 1,
                'status' => 'sent',
                'time' => time(),
            ];
            $flowId = $res->customData['flowId'] ?? null;
            $flowId && $message['flow_id'] = $flowId;

            $urlKey = strtolower($type).'_url';
            $fileUrl = $fields[$urlKey] ?? null;
            $type && $message['file_type'] = strtolower($type);
            $fileUrl && $message['file_url'] = $fileUrl;
            $fileUrl && $message['file_name'] = basename($fileUrl);

            $savedMessage = (object) $message;

            // $savedMessage = Message::create($message);
            // $msgSaveDuration = round((microtime(true) - $startMsgSave) * 1000, 2);
            // Log::info("[CampaignMessageJob:handle] $this->requestId - Message save completed in {$msgSaveDuration}ms");

            // After successful message send and before timeline update
            if ($res !== false && isset($res->customData['messageId'])) {
                // Prepare timeline data
                $timelineData = [
                    'id' => $res->customData['messageId'],
                    'objectId' => $this->contact->id,
                    'data' => [
                        'status' => 'sent',
                        'phone' => $portal->waba_phone,
                        'message' => Func::messageToTimeline($savedMessage),
                    ],
                ];

                // Push timeline data to Redis
                $timelineCacheKey = "timeline_updates:{$this->campaignId}";
                Redis::rpush($timelineCacheKey, json_encode($timelineData));

                // Check Redis length and dispatch timeline batch job if needed
                if (Redis::llen($timelineCacheKey) >= 100) {
                    dispatch(new TimelineBatchJob($this->campaignId))->onQueue('lists');
                }

                // Push campaign data to Redis
                $campaignCacheKey = "campaign_updates:{$this->campaignId}";
                Redis::rpush($campaignCacheKey, json_encode($campaign));

                // Check Redis length and dispatch campaign batch job if needed
                if (Redis::llen($campaignCacheKey) >= 100) {
                    dispatch(new CampaignBatchJob($this->campaignId))->onQueue('lists');
                }

                // Push message data to Redis
                $messageCacheKey = "message_updates:{$this->campaignId}";
                Redis::rpush($messageCacheKey, json_encode($message));

                // Check Redis length and dispatch message batch job if needed
                if (Redis::llen($messageCacheKey) >= 100) {
                    dispatch(new MessageBatchJob($this->campaignId))->onQueue('lists');
                }

                // Update campaign stats
                HubList::where(['account_id' => $this->accountId, 'id' => $this->campaignId])->increment('sent');
            }

            // Start Hubspot timeline update timer
            // $startHubspotUpdate = microtime(true);
            // update hubspot timeline
            // $hsApp->timeline()->update([
            //     'id' => $res->customData['messageId'],
            //     'objectId' => $this->contact->id,
            //     'data' => [
            //         'status' => 'sent',
            //         'phone' => $portal->waba_phone,
            //         'message' => Func::messageToTimeline($savedMessage),
            //     ],
            // ], true);
            // $hubspotUpdateDuration = round((microtime(true) - $startHubspotUpdate) * 1000, 2);
            // Log::info("[CampaignMessageJob:handle] $this->requestId - Hubspot timeline update completed in {$hubspotUpdateDuration}ms");

            // Start campaign stats update timer
            // $startCampaignUpdate = microtime(true);
            // Log::info("[CampaignMessageJob:handle] success for $this->requestId");
            // Campaign::create($campaign);
            // $campaignUpdateDuration = round((microtime(true) - $startCampaignUpdate) * 1000, 2);
            // Log::info("[CampaignMessageJob:handle] $this->requestId - Campaign stats update completed in {$campaignUpdateDuration}ms");

            // Log total successful execution time and summary
            $overallDuration = round((microtime(true) - $startOverall) * 1000, 2);
            Log::info("[CampaignMessageJob:handle] Success for $this->requestId - Total execution time: {$overallDuration}ms");

            // Summary log of all timings
            Log::info("[CampaignMessageJob:performance_summary] $this->requestId - Performance breakdown: " .
                "Initialization: {$initDuration}ms, " .
                "Validation: {$validationDuration}ms, " .
                "Template Fetch: {$templateFetchDuration}ms, " .
                "Parameter Preparation: {$paramPrepDuration}ms, " .
                "API Call: {$apiCallDuration}ms, " .
                "Response Validation: {$respValidationDuration}ms, " .
                "Dialog Save: {$dialogSaveDuration}ms, " .
                // "Message Save: {$msgSaveDuration}ms, " .
                // "Hubspot Update: {$hubspotUpdateDuration}ms, " .
                // "Campaign Update: {$campaignUpdateDuration}ms, " .
                "Total: {$overallDuration}ms"
            );

        } catch (Exception $e) {
            // Start error handling timer
            $startErrorHandling = microtime(true);
            HubList::where(['account_id' => $this->accountId, 'id' => $this->campaignId])->increment('failed');
            $campaign['status'] = 'failed';
            $campaign['message_id'] = $this->requestId;
            $campaign['status_reason'] = $e->getMessage();
            Campaign::create($campaign);
            $errorHandlingDuration = round((microtime(true) - $startErrorHandling) * 1000, 2);
            $overallDuration = round((microtime(true) - $startOverall) * 1000, 2);
            Log::error("[CampaignMessageJob:handle] $this->requestId, Exception after {$overallDuration}ms (error handling: {$errorHandlingDuration}ms): ".$e->getMessage().', Line: '.$e->getLine().', File: '.$e->getFile().', Trace: '.$e->getTraceAsString());
        }

        // Log total execution time
        $overallDuration = round((microtime(true) - $startOverall) * 1000, 2);
        Log::info("[CampaignMessageJob:handle] Completed for $this->requestId - Total execution time: {$overallDuration}ms");
    }

    public function failed(Throwable $e)
    {
        Log::error("[CampaignMessageJob:failed] $this->requestId, Exception: ".$e->getMessage());
    }
}
