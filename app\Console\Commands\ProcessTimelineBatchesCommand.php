<?php

namespace App\Console\Commands;

use Log;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use App\Jobs\TimelineBatchJob;

class ProcessTimelineBatchesCommand extends Command
{
    protected $signature = 'process:timeline-batches';
    protected $description = 'Process any remaining timeline updates that haven\'t reached batch size';

    public function handle()
    {
        $this->info('Starting timeline batch processing...');

        try {
            // Get all campaign timeline keys from Redis
            $keys = Redis::keys('timeline_updates:*');
            
            foreach ($keys as $key) {
                $campaignId = str_replace('timeline_updates:', '', $key);
                $length = Redis::llen($key);

                if ($length > 0) {
                    $this->info("Found {$length} pending timeline updates for campaign {$campaignId}");
                    dispatch(new TimelineBatchJob($campaignId))->onQueue('lists');
                }
            }

            $this->info('Timeline batch processing completed');
        } catch (\Exception $e) {
            Log::error("[ProcessTimelineBatchesCommand] Error: " . $e->getMessage());
            $this->error('Error processing timeline batches: ' . $e->getMessage());
        }
    }
} 