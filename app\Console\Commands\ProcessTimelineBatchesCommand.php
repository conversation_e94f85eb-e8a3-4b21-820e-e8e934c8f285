<?php

namespace App\Console\Commands;

use Log;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use App\Jobs\TimelineBatchJob;
use App\Jobs\CampaignBatchJob;
use App\Jobs\MessageBatchJob;

class ProcessTimelineBatchesCommand extends Command
{
    protected $signature = 'process:timeline-batches';
    protected $description = 'Process any remaining timeline, campaign, and message updates that haven\'t reached batch size';

    public function handle()
    {
        $this->info('Starting batch processing for timeline, campaign, and message updates...');

        try {
            // Process timeline updates
            $this->processTimelineBatches();

            // Process campaign updates
            $this->processCampaignBatches();

            // Process message updates
            $this->processMessageBatches();

            $this->info('All batch processing completed');
        } catch (\Exception $e) {
            Log::error("[ProcessTimelineBatchesCommand] Error: " . $e->getMessage());
            $this->error('Error processing batches: ' . $e->getMessage());
        }
    }

    private function processTimelineBatches()
    {
        $keys = Redis::keys('timeline_updates:*');

        foreach ($keys as $key) {
            $campaignId = str_replace('timeline_updates:', '', $key);
            $length = Redis::llen($key);

            if ($length > 0) {
                $this->info("Found {$length} pending timeline updates for campaign {$campaignId}");
                dispatch(new TimelineBatchJob($campaignId))->onQueue('lists');
            }
        }
    }

    private function processCampaignBatches()
    {
        $keys = Redis::keys('campaign_updates:*');

        foreach ($keys as $key) {
            $campaignId = str_replace('campaign_updates:', '', $key);
            $length = Redis::llen($key);

            if ($length > 0) {
                $this->info("Found {$length} pending campaign updates for campaign {$campaignId}");
                dispatch(new CampaignBatchJob($campaignId))->onQueue('lists');
            }
        }
    }

    private function processMessageBatches()
    {
        $keys = Redis::keys('message_updates:*');

        foreach ($keys as $key) {
            $campaignId = str_replace('message_updates:', '', $key);
            $length = Redis::llen($key);

            if ($length > 0) {
                $this->info("Found {$length} pending message updates for campaign {$campaignId}");
                dispatch(new MessageBatchJob($campaignId))->onQueue('lists');
            }
        }
    }
}