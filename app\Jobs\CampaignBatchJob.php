<?php

namespace App\Jobs;

use Log;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\DB;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class CampaignBatchJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    protected $campaignId;
    protected $requestId;

    public function __construct($campaignId)
    {
        $this->campaignId = $campaignId;
        $this->requestId = round(microtime(true) * 1000).'-campaign-batch-'.$campaignId;
    }

    public function handle(): void
    {
        $startTime = microtime(true);
        Log::info("[CampaignBatchJob:handle] {$this->requestId}, processing batch for campaign: {$this->campaignId}");
        
        $cacheKey = "campaign_updates:{$this->campaignId}";
        $batchSize = 100;
        $batch = [];

        try {
            // Process up to 100 items from Redis
            for ($i = 0; $i < $batchSize; $i++) {
                $item = Redis::lpop($cacheKey);
                if (!$item) {
                    break;
                }
                $batch[] = json_decode($item, true);
            }

            if (!empty($batch)) {
                try {
                    // Insert campaign data into database
                    DB::table('campaigns')->insert($batch);
                    Log::info("[CampaignBatchJob:handle] {$this->requestId}, Successfully processed batch of " . count($batch) . " campaign records");
                } catch (Exception $e) {
                    // If batch insert fails, push items back to Redis
                    Log::error("[CampaignBatchJob:handle] {$this->requestId}, Batch insert failed: " . $e->getMessage());
                    foreach ($batch as $item) {
                        Redis::lpush($cacheKey, json_encode($item));
                    }
                    throw $e;
                }
            }

        } catch (Exception $e) {
            Log::error("[CampaignBatchJob:handle] {$this->requestId}, Error: " . $e->getMessage());
            throw $e;
        } finally {
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            Log::info("[CampaignBatchJob:handle] {$this->requestId}, Completed in {$duration} ms");
        }
    }

    public function failed(Exception $e)
    {
        Log::error("[CampaignBatchJob:failed] {$this->requestId}, Exception: " . $e->getMessage());
    }
}
